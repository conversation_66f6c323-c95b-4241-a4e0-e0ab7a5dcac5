import { PayFieldsConfig, BillingAddress } from "../types/payfields.types";
import { isInIframe } from "./iframe-communication";
import { isProduction } from "../../../config/payrixFieldMapping";

interface InitializeOptions {
  onSuccess: (response: unknown) => void;
  onFailure: (error: unknown) => void;
  onValidationFailure: (error: unknown) => void;
  onFinish: (response: unknown) => void;
  onReady?: () => void;
  billingAddress?: BillingAddress;
}

interface GooglePayConfig {
  enabled: boolean;
  environment?: "TEST" | "PRODUCTION";
}

export const validatePayFieldsConfig = (config: PayFieldsConfig): void => {
  if (!config.publicKey || !config.merchantId) {
    throw new Error(`Missing required PayFields configuration: ${!config.publicKey ? "publicKey" : "merchantId"}`);
  }
};

export const checkDOMElements = (): boolean => {
  const cardNumberEl = document.getElementById("card-number");
  const cardNameEl = document.getElementById("card-name");
  const cardCvvEl = document.getElementById("card-cvv");
  const cardExpirationEl = document.getElementById("card-expiration");

  if (!cardNumberEl || !cardNameEl || !cardCvvEl || !cardExpirationEl) {
    return false;
  }
  return true;
};

export const configurePayFields = (config: PayFieldsConfig, billingAddress?: BillingAddress): void => {
  if (!window.PayFields) {
    throw new Error("PayFields not loaded");
  }

  window.PayFields.config.apiKey = config.publicKey;
  window.PayFields.config.merchant = config.merchantId;

  // Initialize Google Pay config object if it doesn't exist
  if (config.googlePayConfig?.enabled) {
    if (!window.PayFields.config.googlePay) {
      window.PayFields.config.googlePay = {} as GooglePayConfig;
    }
    window.PayFields.config.googlePay.enabled = true;

    // Only set environment for non-production (TEST)
    // In production, leave environment undefined to use Google Pay's default
    if (!isProduction) {
      window.PayFields.config.googlePay.environment = "TEST";
    }

    // Ensure Google Pay button container exists
    const googlePayContainer = document.getElementById("googlePayButton");
    if (googlePayContainer) {
      googlePayContainer.style.display = "block";
    }
  } else {
    // Hide Google Pay button if not enabled
    const googlePayContainer = document.getElementById("googlePayButton");
    if (googlePayContainer) {
      googlePayContainer.style.display = "none";
    }
  }

  window.PayFields.config.mode = config.mode;
  window.PayFields.config.txnType = config.txnType;
  window.PayFields.config.description = config.description;

  // Set the actual amount for proper display in Apple Pay and Google Pay
  // Token mode still generates tokens for PCI compliance, but shows real amount to users
  window.PayFields.config.amount = config.amount;

  if (isInIframe()) {
    window.PayFields.config.iframe = true;
    window.PayFields.config.responsive = true;
    window.PayFields.config.autoResize = true;
  }

  if (billingAddress) {
    window.PayFields.config.billing = {
      name: `${billingAddress.firstName} ${billingAddress.lastName}`,
      email: billingAddress.email,
      phone: billingAddress.phone,
      address: billingAddress.line1,
      address2: billingAddress.line2,
      city: billingAddress.city,
      state: billingAddress.state,
      zip: billingAddress.zip,
      country: billingAddress.country,
    };
    window.PayFields.config.name = `${billingAddress.firstName} ${billingAddress.lastName}`;
  }
};

export const setupPayFieldsElements = (): void => {
  if (!window.PayFields) return;

  const fields = [
    { type: "number", element: "#card-number" },
    { type: "name", element: "#card-name" },
    { type: "cvv", element: "#card-cvv" },
    { type: "expiration", element: "#card-expiration" },
  ];

  window.PayFields.fields = fields;
};

export const applyPayFieldsStyles = (): void => {
  if (!window.PayFields) return;

  const baseInputStyle = isInIframe()
    ? {
        fontSize: "16px",
        padding: "0.625rem 0.875rem",
      }
    : {
        fontSize: "0.875rem",
        padding: "0.75rem 1rem",
      };

  window.PayFields.customizations.style = {
    ".input": {
      display: "block",
      width: "100%",
      padding: baseInputStyle.padding,
      fontSize: baseInputStyle.fontSize,
      fontWeight: "400",
      lineHeight: "1.5rem",
      backgroundColor: "#fff",
      border: "1px solid #e2e8f0",
      borderRadius: "0.375rem",
      appearance: "none",
    },
    ".form-error": {
      color: "#e53e3e",
      fontSize: "0.75rem",
      marginTop: "0.25rem",
    },
  };

  window.PayFields.customizations.placeholders = {
    "#expiration": "MM/YY",
    "#payment_cvv": "CVV",
    "#payment_number": "0000 0000 0000 0000",
    "#name": "Full Name on Card",
  };
};

export const resetPayFields = (): void => {
  if (window.PayFields && typeof window.PayFields.unmountAll === "function") {
    window.PayFields.unmountAll();
  }

  if (window.PayFields) {
    window.PayFields.onSuccess = undefined;
    window.PayFields.onFailure = undefined;
    window.PayFields.onValidationFailure = undefined;
    window.PayFields.onFinish = undefined;
  }
};

export const initializePayFields = (config: PayFieldsConfig, options: InitializeOptions): void => {
  if (!checkDOMElements()) {
    throw new Error("Required payment form elements not found in DOM");
  }

  // Reset PayFields before initializing with new config
  resetPayFields();

  validatePayFieldsConfig(config);
  configurePayFields(config, options.billingAddress);
  setupPayFieldsElements();
  applyPayFieldsStyles();

  window.PayFields.onSuccess = options.onSuccess;
  window.PayFields.onFailure = options.onFailure;
  window.PayFields.onValidationFailure = options.onValidationFailure;
  window.PayFields.onFinish = options.onFinish;

  window.PayFields.ready();

  // Call onReady callback after PayFields is ready
  if (options.onReady) {
    options.onReady();
  }
};

export const updateBillingAddress = (billingAddress: BillingAddress): void => {
  if (!window.PayFields || !billingAddress) return;

  window.PayFields.config.billing = {
    name: `${billingAddress.firstName} ${billingAddress.lastName}`,
    email: billingAddress.email,
    phone: billingAddress.phone,
    address: billingAddress.line1,
    address2: billingAddress.line2,
    city: billingAddress.city,
    state: billingAddress.state,
    zip: billingAddress.zip,
    country: billingAddress.country,
  };

  window.PayFields.config.name = `${billingAddress.firstName} ${billingAddress.lastName}`;
};
